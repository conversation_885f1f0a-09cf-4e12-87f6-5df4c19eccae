import React from 'react';
import Link from 'next/link';
import Logo from './Logo';

const Footer = () => {
  return (
    <footer className="bg-gray-900 text-white mt-auto">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 py-16">
          {/* Brand Section */}
          <div className="lg:col-span-2">
            <div className="mb-6">
              <Logo className="w-12 h-12" showText={true} textSize="text-xl" variant="dark" />
            </div>
            <p className="text-gray-300 mb-4 max-w-md leading-relaxed">
              Empowering students to master Spanish through interactive lessons, cultural immersion, and personalized learning paths.
            </p>
            <p className="text-accent-400 font-medium italic">
              "¡Aprende español, conecta con el mundo!"
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-accent-500 font-semibold text-lg mb-6">Quick Links</h3>
            <ul className="space-y-3">
              <li><Link href="/about" className="text-gray-300 hover:text-accent-500 transition-colors">About Us</Link></li>
              <li><Link href="/courses" className="text-gray-300 hover:text-accent-500 transition-colors">Courses</Link></li>
              <li><Link href="/tutors" className="text-gray-300 hover:text-accent-500 transition-colors">Tutors</Link></li>
              <li><Link href="/blog" className="text-gray-300 hover:text-accent-500 transition-colors">Blog</Link></li>
              <li><Link href="/contact" className="text-gray-300 hover:text-accent-500 transition-colors">Contact</Link></li>
            </ul>
          </div>

          {/* Courses & Contact */}
          <div>
            <h3 className="text-accent-500 font-semibold text-lg mb-6">Courses</h3>
            <ul className="space-y-3 mb-8">
              <li><Link href="/courses/beginner" className="text-gray-300 hover:text-accent-500 transition-colors">Beginner Spanish</Link></li>
              <li><Link href="/courses/intermediate" className="text-gray-300 hover:text-accent-500 transition-colors">Intermediate Spanish</Link></li>
              <li><Link href="/courses/advanced" className="text-gray-300 hover:text-accent-500 transition-colors">Advanced Spanish</Link></li>
            </ul>

            <h3 className="text-accent-500 font-semibold text-lg mb-4">Contact Info</h3>
            <div className="space-y-2 text-gray-300 text-sm">
              <p>📍 Madrid, Spain</p>
              <p>📧 <EMAIL></p>
              <p>📞 +34 123 456 789</p>
            </div>

            <div className="flex space-x-4 mt-6">
              <a href="#" aria-label="Facebook" className="text-2xl hover:scale-110 transition-transform">📘</a>
              <a href="#" aria-label="Twitter" className="text-2xl hover:scale-110 transition-transform">🐦</a>
              <a href="#" aria-label="Instagram" className="text-2xl hover:scale-110 transition-transform">📷</a>
              <a href="#" aria-label="LinkedIn" className="text-2xl hover:scale-110 transition-transform">💼</a>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-700 py-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-gray-400 text-sm">&copy; 2024 Spanish Learning Academy. All rights reserved.</p>
            <div className="flex space-x-6">
              <Link href="/privacy" className="text-gray-400 hover:text-accent-500 text-sm transition-colors">Privacy Policy</Link>
              <Link href="/terms" className="text-gray-400 hover:text-accent-500 text-sm transition-colors">Terms of Service</Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
