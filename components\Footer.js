import React from 'react';
import { Link } from 'react-router-dom';
import Logo from './Logo';

const Footer = () => {
  return (
    <footer className="bg-gray-900 text-white mt-auto">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 py-16">
          {/* Brand Section */}
          <div className="lg:col-span-2">
            <div className="mb-6">
              <Logo className="w-12 h-12" showText={true} textSize="text-xl" variant="dark" />
            </div>
            <p className="text-gray-300 mb-4 max-w-md leading-relaxed">
              Empowering underprivileged youth through education, mentorship, and digital skills training.
            </p>
            <p className="text-accent-400 font-medium italic">
              "Bridging the gap, building the future."
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-accent-500 font-semibold text-lg mb-6">Quick Links</h3>
            <ul className="space-y-3">
              <li><Link to="/about" className="text-gray-300 hover:text-accent-500 transition-colors">About Us</Link></li>
              <li><Link to="/programs" className="text-gray-300 hover:text-accent-500 transition-colors">Programs</Link></li>
              <li><Link to="/volunteer" className="text-gray-300 hover:text-accent-500 transition-colors">Volunteer</Link></li>
              <li><Link to="/blog" className="text-gray-300 hover:text-accent-500 transition-colors">Blog</Link></li>
              <li><Link to="/contact" className="text-gray-300 hover:text-accent-500 transition-colors">Contact</Link></li>
            </ul>
          </div>

          {/* Programs & Contact */}
          <div>
            <h3 className="text-accent-500 font-semibold text-lg mb-6">Programs</h3>
            <ul className="space-y-3 mb-8">
              <li><Link to="/programs#code-for-hope" className="text-gray-300 hover:text-accent-500 transition-colors">Code for Hope</Link></li>
              <li><Link to="/programs#back2school" className="text-gray-300 hover:text-accent-500 transition-colors">Back2School</Link></li>
              <li><Link to="/programs#mentormatch" className="text-gray-300 hover:text-accent-500 transition-colors">MentorMatch</Link></li>
            </ul>

            <h3 className="text-accent-500 font-semibold text-lg mb-4">Contact Info</h3>
            <div className="space-y-2 text-gray-300 text-sm">
              <p>📍 Yaba, Lagos, Nigeria</p>
              <p>📧 <EMAIL></p>
              <p>📞 +234 (0) 123 456 7890</p>
            </div>

            <div className="flex space-x-4 mt-6">
              <a href="#" aria-label="Facebook" className="text-2xl hover:scale-110 transition-transform">📘</a>
              <a href="#" aria-label="Twitter" className="text-2xl hover:scale-110 transition-transform">🐦</a>
              <a href="#" aria-label="Instagram" className="text-2xl hover:scale-110 transition-transform">📷</a>
              <a href="#" aria-label="LinkedIn" className="text-2xl hover:scale-110 transition-transform">💼</a>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-700 py-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-gray-400 text-sm">&copy; 2024 HopeBridge Foundation. All rights reserved.</p>
            <div className="flex space-x-6">
              <Link to="/privacy" className="text-gray-400 hover:text-accent-500 text-sm transition-colors">Privacy Policy</Link>
              <Link to="/terms" className="text-gray-400 hover:text-accent-500 text-sm transition-colors">Terms of Service</Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
