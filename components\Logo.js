import React from 'react';

const Logo = ({ className = "w-10 h-10", showText = true, textSize = "text-xl", variant = "light" }) => {
  const isLight = variant === "light";
  const textColor = isLight ? "text-primary-600" : "text-white";
  const taglineColor = isLight ? "text-accent-500" : "text-accent-500";
  return (
    <div className="flex items-center space-x-3">
      {/* SVG Logo */}
      <div className={`${className} relative`}>
        <svg
          viewBox="0 0 100 100"
          className="w-full h-full"
          xmlns="http://www.w3.org/2000/svg"
        >
          {/* Background Circle */}
          <circle
            cx="50"
            cy="50"
            r="48"
            fill="url(#logoGradient)"
            stroke="url(#borderGradient)"
            strokeWidth="2"
          />
          
          {/* Bridge Structure */}
          {/* Main Bridge Arch */}
          <path
            d="M 20 65 Q 50 35 80 65"
            stroke="#ffffff"
            strokeWidth="3"
            fill="none"
            strokeLinecap="round"
          />
          
          {/* Bridge Deck */}
          <line
            x1="20"
            y1="65"
            x2="80"
            y2="65"
            stroke="#ffffff"
            strokeWidth="4"
            strokeLinecap="round"
          />
          
          {/* Support Pillars */}
          <line
            x1="35"
            y1="65"
            x2="35"
            y2="75"
            stroke="#ffffff"
            strokeWidth="2"
            strokeLinecap="round"
          />
          <line
            x1="50"
            y1="65"
            x2="50"
            y2="75"
            stroke="#ffffff"
            strokeWidth="2"
            strokeLinecap="round"
          />
          <line
            x1="65"
            y1="65"
            x2="65"
            y2="75"
            stroke="#ffffff"
            strokeWidth="2"
            strokeLinecap="round"
          />
          
          {/* Hope Symbol - Rising Sun/Star */}
          <circle
            cx="50"
            cy="25"
            r="8"
            fill="#fbbf24"
            opacity="0.9"
          />
          
          {/* Sun Rays */}
          <g stroke="#fbbf24" strokeWidth="2" strokeLinecap="round" opacity="0.7">
            <line x1="50" y1="12" x2="50" y2="18" />
            <line x1="62" y1="17" x2="58" y2="21" />
            <line x1="67" y1="25" x2="61" y2="25" />
            <line x1="62" y1="33" x2="58" y2="29" />
            <line x1="50" y1="38" x2="50" y2="32" />
            <line x1="38" y1="33" x2="42" y2="29" />
            <line x1="33" y1="25" x2="39" y2="25" />
            <line x1="38" y1="17" x2="42" y2="21" />
          </g>
          
          {/* Connection Lines (representing community) */}
          <g stroke="#ffffff" strokeWidth="1" opacity="0.6">
            <line x1="25" y1="55" x2="35" y2="45" />
            <line x1="75" y1="55" x2="65" y2="45" />
            <line x1="45" y1="50" x2="55" y2="40" />
          </g>
          
          {/* Gradients */}
          <defs>
            <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#2563eb" />
              <stop offset="50%" stopColor="#1e40af" />
              <stop offset="100%" stopColor="#1d4ed8" />
            </linearGradient>
            <linearGradient id="borderGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#f59e0b" />
              <stop offset="100%" stopColor="#fbbf24" />
            </linearGradient>
          </defs>
        </svg>
      </div>
      
      {/* Text Logo */}
      {showText && (
        <div className="flex flex-col">
          <span className={`${textSize} font-bold ${textColor} leading-tight`}>
            HopeBridge
          </span>
          <span className={`text-sm font-medium ${taglineColor} leading-tight`}>
            Foundation
          </span>
        </div>
      )}
    </div>
  );
};

export default Logo;
