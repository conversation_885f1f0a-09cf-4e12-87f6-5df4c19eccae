'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import Logo from './Logo';

const Navbar = () => {
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const pathname = usePathname();

    const toggleMenu = () => {
      setIsMenuOpen(!isMenuOpen);
    };

    const isActive = (path) => {
      return pathname === path;
    };
    return (
        <nav className="bg-white shadow-lg sticky top-0 z-50">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex justify-between items-center py-4">
                    <Link href="/" className="text-gray-800 hover:text-blue-600 transition-colors">
                        <Logo className="w-12 h-12" showText={true} textSize="text-xl" />
                    </Link>
                    <div className="hidden md:flex items-center space-x-8">
                        <Link href="/" className={`font-medium transition-colors relative ${
                            isActive('/') ? 'text-blue-600 after:absolute after:bottom-[-8px] after:left-0 after:right-0 after:h-0.5 after:bg-blue-600'
                             : 'text-gray-700 hover:text-blue-600'
                        }`}>
                            Home
                        </Link>
                        <Link href="/about" className={`font-medium transition-colors relative ${
                            isActive('/about') ? 'text-blue-600 after:absolute after:bottom-[-8px] after:left-0 after:right-0 after:h-0.5 after:bg-blue-600'
                             : 'text-gray-700 hover:text-blue-600'
                        }`}>
                            About
                        </Link>
                        <Link href="/programs" className={`font-medium transition-colors relative ${
                            isActive('/programs') ? 'text-blue-600 after:absolute after:bottom-[-8px] after:left-0 after:right-0 after:h-0.5 after:bg-blue-600'
                             : 'text-gray-700 hover:text-blue-600'
                        }`}>
                            Programs
                        </Link>
                        <Link href="/volunteer" className={`font-medium transition-colors relative ${
                            isActive('/volunteer') ? 'text-blue-600 after:absolute after:bottom-[-8px] after:left-0 after:right-0 after:h-0.5 after:bg-blue-600'
                             : 'text-gray-700 hover:text-blue-600'
                        }`}>
                            Volunteer
                        </Link>
                        <Link href="/blog" className={`font-medium transition-colors relative ${
                            isActive('/blog') ? 'text-blue-600 after:absolute after:bottom-[-8px] after:left-0 after:right-0 after:h-0.5 after:bg-blue-600'
                             : 'text-gray-700 hover:text-blue-600'
                        }`}>
                            Blog
                        </Link>
                        <Link href="/contact" className={`font-medium transition-colors relative ${
                            isActive('/contact') ? 'text-blue-600 after:absolute after:bottom-[-8px] after:left-0 after:right-0 after:h-0.5 after:bg-blue-600'
                             : 'text-gray-700 hover:text-blue-600'
                        }`}>
                            Contact
                        </Link>
                        <Link href="/donate" className="bg-blue-500 text-white rounded-full px-6 py-2 font-medium hover:bg-blue-600 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl">
                            Donate
                        </Link>
                    </div>

                    {/* Mobile menu button */}
                    <button
                        className="md:hidden flex flex-col space-y-1 p-2"
                        onClick={toggleMenu}
                        aria-label="Toggle navigation menu"
                    >
                        <span className={`w-6 h-0.5 bg-gray-700 transition-all duration-300 ${isMenuOpen ? 'rotate-45 translate-y-1.5' : ''}`}></span>
                        <span className={`w-6 h-0.5 bg-gray-700 transition-all duration-300 ${isMenuOpen ? 'opacity-0' : ''}`}></span>
                        <span className={`w-6 h-0.5 bg-gray-700 transition-all duration-300 ${isMenuOpen ? '-rotate-45 -translate-y-1.5' : ''}`}></span>
                    </button>
                </div>

                {/* Mobile menu */}
                <div className={`md:hidden opacity-0.8 transition-all duration-300 ease-in-out ${isMenuOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0 overflow-hidden'}`}>
                    <div className="py-4 space-y-4 border-t border-gray-200">
                        <Link href="/" className={`block font-medium transition-colors ${
                            isActive('/') ? 'text-blue-600' : 'text-gray-700 hover:text-blue-600'
                        }`} onClick={() => setIsMenuOpen(false)}>
                            Home
                        </Link>
                        <Link href="/about" className={`block font-medium transition-colors ${
                            isActive('/about') ? 'text-blue-600' : 'text-gray-700 hover:text-blue-600'
                        }`} onClick={() => setIsMenuOpen(false)}>
                            About
                        </Link>
                        <Link href="/programs" className={`block font-medium transition-colors ${
                            isActive('/programs') ? 'text-blue-600' : 'text-gray-700 hover:text-blue-600'
                        }`} onClick={() => setIsMenuOpen(false)}>
                            Programs
                        </Link>
                        <Link href="/volunteer" className={`block font-medium transition-colors ${
                            isActive('/volunteer') ? 'text-blue-600' : 'text-gray-700 hover:text-blue-600'
                        }`} onClick={() => setIsMenuOpen(false)}>
                            Volunteer
                        </Link>
                        <Link href="/blog" className={`block font-medium transition-colors ${
                            isActive('/blog') ? 'text-blue-600' : 'text-gray-700 hover:text-blue-600'
                        }`} onClick={() => setIsMenuOpen(false)}>
                            Blog
                        </Link>
                        <Link href="/contact" className={`block font-medium transition-colors ${
                            isActive('/contact') ? 'text-blue-600' : 'text-gray-700 hover:text-blue-600'
                        }`} onClick={() => setIsMenuOpen(false)}>
                            Contact
                        </Link>
                        <Link href="/donate"
                            className="inline-block bg-blue-500 text-white rounded-full px-6 py-2 font-medium hover:bg-blue-600 transition-colors"
                            onClick={() => setIsMenuOpen(false)}
                        >
                            Donate
                        </Link>
                    </div>
                </div>
            </div>
        </nav>
    );
};

export default Navbar;