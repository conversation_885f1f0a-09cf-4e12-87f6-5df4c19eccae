import React from 'react';
import Link from 'next/link';

const Navbar = () => {
    return (
        <nav className="bg-white shadow-lg sticky top-0 z-50">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex justify-between items-center py-4">
                    <Link href="/" className="text-gray-800 hover:text-blue-600 transition-colors">
                        <span className="text-xl font-bold">Spanish Next</span>
                    </Link>
                    <ul className="flex gap-6">
                        <li>
                            <Link className="text-gray-700 hover:text-blue-600 transition-colors font-medium" href="/">
                                Home
                            </Link>
                        </li>
                        <li>
                            <Link className="text-gray-700 hover:text-blue-600 transition-colors font-medium" href="/about">
                                About Us
                            </Link>
                        </li>
                        <li>
                            <Link className="text-gray-700 hover:text-blue-600 transition-colors font-medium" href="/contact">
                                Contact
                            </Link>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    );
};

export default Navbar;