import Link from "next/link";
import Logo from "@/components/Logo";

export default function Home() {
  return (
    <div>
      <section className="relative bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 text-white overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent"></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="text-center lg:text-left">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight mb-6">
                Empowering Youth, <span className="text-yellow-300">Building Futures</span>
              </h1>
              <p className="text-xl md:text-2xl text-blue-100 mb-8 leading-relaxed">
                Spanish Next is dedicated to empowering students through
                Spanish language education, cultural immersion, and digital learning tools.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                <Link href="/donate" className="bg-yellow-500 text-white px-8 py-4 rounded-full font-semibold text-lg hover:bg-yellow-600 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl">
                  Donate Now
                </Link>
                <Link
                  href="/volunteer"
                  className="border-2 border-white text-white px-8 py-4 rounded-full font-semibold text-lg hover:bg-white hover:text-blue-600 transition-all duration-200"
                >
                  Become a Volunteer
                </Link>
              </div>
            </div>
            <div className="flex justify-center lg:justify-end">
              <div className="bg-white/10 backdrop-blur-sm rounded-3xl p-8 text-center border border-white/20 shadow-2xl">
                <div className="mb-4 flex justify-center">
                  <Logo className="w-20 h-20" showText={false} />
                </div>
                <p className="text-lg font-medium text-blue-100">Learning Spanish Together</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transform hover:-translate-y-2 transition-all duration-300">
              <div className="text-4xl lg:text-5xl font-bold text-blue-600 mb-2">500+</div>
              <div className="text-gray-600 font-medium">Students Trained</div>
            </div>
            <div className="text-center bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transform hover:-translate-y-2 transition-all duration-300">
              <div className="text-4xl lg:text-5xl font-bold text-blue-600 mb-2">30</div>
              <div className="text-gray-600 font-medium">Scholarships Awarded</div>
            </div>
            <div className="text-center bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transform hover:-translate-y-2 transition-all duration-300">
              <div className="text-4xl lg:text-5xl font-bold text-blue-600 mb-2">15</div>
              <div className="text-gray-600 font-medium">Partner Organizations</div>
            </div>
            <div className="text-center bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transform hover:-translate-y-2 transition-all duration-300">
              <div className="text-4xl lg:text-5xl font-bold text-blue-600 mb-2">3</div>
              <div className="text-gray-600 font-medium">Years of Impact</div>
            </div>
          </div>
        </div>
      </section>

      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
         <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">Our Programs</h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">Transforming lives through education and technology</p>
         </div>
         <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
         <div className="group bg-white rounded-3xl p-8 shadow-lg hover:shadow-2xl transform hover:-translate-y-4 transition-all duration-300 border border-gray-100">
              <div className="text-6xl mb-6">�</div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Spanish Basics</h3>
              <p className="text-gray-600 mb-6 leading-relaxed">Learn fundamental Spanish vocabulary, grammar, and conversation skills.</p>
              <Link
                href="/programs#spanish-basics"
                className="inline-flex items-center text-blue-600 font-semibold hover:text-blue-700 transition-colors transform duration-200"
              >
                Learn More
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </Link>
            </div>
            <div className="group bg-white rounded-3xl p-8 shadow-lg hover:shadow-2xl transform hover:-translate-y-4 transition-all duration-300 border border-gray-100">
              <div className="text-6xl mb-6">�️</div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Conversation Practice</h3>
              <p className="text-gray-600 mb-6 leading-relaxed">Interactive speaking sessions with native Spanish speakers.</p>
              <Link
                href="/programs#conversation"
                className="inline-flex items-center text-blue-600 font-semibold hover:text-blue-700 transition-colors transform duration-200"
              >
                Learn More
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </Link>
            </div>
            <div className="group bg-white rounded-3xl p-8 shadow-lg hover:shadow-2xl transform hover:-translate-y-4 transition-all duration-300 border border-gray-100">
              <div className="text-6xl mb-6">🌍</div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Cultural Immersion</h3>
              <p className="text-gray-600 mb-6 leading-relaxed">Explore Spanish-speaking cultures through virtual experiences and activities.</p>
              <Link
                href="/programs#culture"
                className="inline-flex items-center text-blue-600 font-semibold hover:text-blue-700 transition-colors transform duration-200"
              >
                Learn More
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </Link>
            </div>
         </div>
        </div>
      </section>

      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">Our Mission</h2>
              <p className="text-2xl text-primary-600 font-semibold italic mb-6 leading-relaxed">
                "Empowering underprivileged youth through education, mentorship, and digital skills training."
              </p>
              <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                At HopeBridge Foundation, we believe every young person deserves the opportunity to
                build a brighter future. Through our comprehensive programs, we provide the tools,
                knowledge, and support needed to break the cycle of poverty and create lasting change
                in communities across Lagos, Nigeria.
              </p>
              <Link
                to="/about"
                className="inline-flex items-center bg-primary-600 text-white px-8 py-4 rounded-full font-semibold hover:bg-primary-700 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                Learn About Our Story
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </Link>
            </div>
            <div className="flex justify-center">
              <div className="bg-white rounded-3xl p-12 shadow-xl text-center border border-gray-200">
                <div className="mb-4 flex justify-center">
                  <Logo className="w-24 h-24" showText={false} variant="light" />
                </div>
                <p className="text-lg font-medium text-gray-600">Building Bridges to Success</p>
              </div>
            </div>
          </div>
        </div>
      </section>


      <section className="py-20 bg-gradient-to-r from-accent-500 to-accent-600 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl lg:text-5xl font-bold mb-6">Ready to Make a Difference?</h2>
          <p className="text-xl mb-10 opacity-90">Join us in empowering the next generation of leaders and innovators.</p>
          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Link
              to="/volunteer"
              className="bg-white text-accent-600 px-8 py-4 rounded-full font-semibold text-lg hover:bg-gray-100 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              Volunteer With Us
            </Link>
            <Link
              to="/donate"
              className="border-2 border-white text-white px-8 py-4 rounded-full font-semibold text-lg hover:bg-white hover:text-accent-600 transition-all duration-200"
            >
              Support Our Cause
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
