import Image from "next/image";
import Link from "next/link";
import Logo from "@/components/Logo";

export default function Home() {
  return (
    <div>
      <section className="relative bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 text-white overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent"></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="text-center lg:text-left">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight mb-6">
                Empowering Youth, <span className="text-yellow-400">Building Futures</span>
              </h1>
              <p className="text-xl md:text-2xl text-blue-100 mb-8 leading-relaxed">
                Spanish Next is dedicated to empowering students through
                Spanish language education, cultural immersion, and digital learning tools.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                <Link href="/donate" className="bg-yellow-500 text-white px-8 py-4 rounded-full font-semibold text-lg hover:bg-yellow-600 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl">
                  Donate Now
                </Link>
                <Link
                  href="/volunteer"
                  className="border-2 border-white text-white px-8 py-4 rounded-full font-semibold text-lg hover:bg-white hover:text-blue-600 transition-all duration-200"
                >
                  Become a Volunteer
                </Link>
              </div>
            </div>
            <div className="flex justify-center lg:justify-end">
              <div className="bg-white/10 backdrop-blur-sm rounded-3xl p-8 text-center border border-white/20 shadow-2xl">
                <div className="mb-4 flex justify-center">
                  <Logo className="w-20 h-20" showText={false} />
                </div>
                <p className="text-lg font-medium text-blue-100">Learning Spanish Together</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
