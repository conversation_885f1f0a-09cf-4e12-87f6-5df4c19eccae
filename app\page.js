import Image from "next/image";

export default function Home() {
  return (
    <div>
      <section className="relative bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 text-white overflow-hidden">
        <div className="absolute inset-0 bg-black bg-opacity-10"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent"></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="text-center lg:text-left animate-fade-in">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight mb-6">
                Empowering Youth, <span className="text-accent-400">Building Futures</span>
              </h1>
              <p className="text-xl md:text-2xl text-blue-100 mb-8 leading-relaxed">
                HopeBridge Foundation is dedicated to empowering underprivileged youth through
                education, mentorship, and digital skills training in Lagos, Nigeria.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                <Link href="/donate" className="bg-accent-500 text-white px-8bpy-4 rounded-full font-semibold text-lg hover:bg-accent-600 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl">
                  Donate Now
                </Link>
                <Link
                  to="/volunteer"
                  className="border-2 border-white text-white px-8 py-4 rounded-full font-semibold text-lg hover:bg-white hover:text-primary-600 transition-all duration-200"
                >
                  Become a Volunteer
                </Link>
              </div>
            </div>
            <div className="flex justify-center lg:justify-end animate-slide-up">
              <div className="bg-white/10 backdrop-blur-sm rounded-3xl p-8 text-center border border-white/20 shadow-2xl">
                <div className="mb-4 flex justify-center">
                  <Logo className="w-20 h-20" showText={false} variant="dark" />
                </div>
                <p className="text-lg font-medium text-blue-100">Youth Learning Together</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
